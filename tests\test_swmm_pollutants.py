import os
import unittest
import shutil
import pandas as pd
import numpy as np
import swmmio
from pyswmm import Simulation, Output
from swmmio.utils.modify_model import replace_inp_section

from tests.test_helper import TestHelper
from tests.data import TESTS_DATA_PATH, TESTS_OUTPUT_PATH

class TestSwmmPollutants(unittest.TestCase):

    def setUp(self):
        self.output_folder = os.path.join(TESTS_OUTPUT_PATH, 'swmm_pollutants')
        os.makedirs(self.output_folder, exist_ok=True)
        self.helper = TestHelper()

        # Define pollutants data for use in tests
        self.pollutants_data = {
            'Name': ['TSS', 'Lead', 'Zinc', 'Oil'],
            'Units': ['MG/L', 'UG/L', 'UG/L', 'MG/L'],
            'Crain': [0, 0, 0, 0],
            'Cgw': [0, 0, 0, 0],
            'Kdecay': [0.05, 0.02, 0.03, 0.1],
            'SnowOnly': [0, 0, 0, 0],  # SWMM expects 0 or 1, not 'NO'
            'CoPollutant': ['', '', '', ''],  # Empty string instead of '*'
            'CoFrac': [0, 0, 0, 0],
            'Cdwf': [0, 0, 0, 0],
            'Cinit': [0, 0, 0, 0]
        }
    
    def test_add_road_pollutants(self):
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'road_pollutants_test.inp')
        
        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)
        
        # Load the model using swmmio and modify it directly
        model = swmmio.Model(test_swmm_file)

        # Save model to temp file first (without land use modifications)
        temp_file = test_swmm_file.replace('.inp', '_temp.inp')
        model.inp.save(temp_file)

        # Get subcatchments info for land use assignment
        subcatchments = model.inp.subcatchments.copy()
        # For simplicity, assign all subcatchments to 'Residential' land use
        subcatchments['Landuse'] = 'Residential'

        # Now manually add pollutant sections to the file
        # Pass the subcatchments with land use information directly
        self._add_pollutant_sections_to_file(temp_file, test_swmm_file, subcatchments)

        # Clean up temporary file (commented out for debugging)
        # if os.path.exists(temp_file):
        #     os.remove(temp_file)
        
        # Run the simulation
        output_file = os.path.join(self.output_folder, 'road_pollutants_results.out')
        report_file = os.path.join(self.output_folder, 'road_pollutants_report.rpt')
        
        # Change to output directory to run simulation
        original_dir = os.getcwd()
        os.chdir(self.output_folder)
        
        try:
            with Simulation(os.path.basename(test_swmm_file)) as sim:
                for step in sim:
                    pass
            
            # Check if output file was created
            self.assertTrue(os.path.exists(output_file), "Output file was not created")
            
            # Analyze results using pyswmm Output
            with Output(output_file) as out:
                # Get outfall nodes
                outfalls = [node for node in model.inp.outfalls.index]
                
                for outfall in outfalls:
                    print(f"Pollutant concentrations at outfall {outfall}:")
                    
                    # Get pollutant results for this node
                    for pollutant in self.pollutants_data['Name']:
                        try:
                            # Get pollutant concentration time series
                            pollutant_index = out.pollutants.index(pollutant)
                            conc_series = []
                            
                            for time_idx in range(out.num_periods):
                                conc = out.node_pollutant_quality(outfall, pollutant_index, time_idx)
                                conc_series.append(conc)
                            
                            # Convert to numpy array for calculations
                            conc_array = np.array(conc_series)
                            
                            # Print results
                            print(f"  {pollutant}: Max={np.max(conc_array)}, Avg={np.mean(conc_array)}")
                            
                            # Assert that concentrations are within expected ranges
                            self.assertGreaterEqual(np.max(conc_array), 0, 
                                                  f"Maximum {pollutant} concentration should be non-negative")
                        except Exception as e:
                            print(f"Error getting data for {pollutant}: {str(e)}")
        
        finally:
            # Change back to original directory
            os.chdir(original_dir)

    def _add_pollutant_sections_to_file(self, temp_file, output_file, subcatchments_with_landuse):
        """Helper method to add pollutant sections to SWMM file"""

        # Read the temporary file
        with open(temp_file, 'r') as f:
            lines = f.readlines()

        # Find where to insert LANDUSES section (before SUBCATCHMENTS)
        subcatchments_index = -1
        for i, line in enumerate(lines):
            if line.strip() == '[SUBCATCHMENTS]':
                subcatchments_index = i
                break

        if subcatchments_index == -1:
            raise ValueError("Could not find [SUBCATCHMENTS] section in the file")

        # Use the passed subcatchments data to find all used land uses
        subcatchments = subcatchments_with_landuse

        # Check if Landuse column exists, if not use default land uses
        if 'Landuse' in subcatchments.columns:
            used_landuses = subcatchments['Landuse'].dropna().unique()
        else:
            used_landuses = []

        if len(used_landuses) == 0:
            used_landuses = ['Residential']  # fallback if none found

        # Step 1: LANDUSES section
        landuses_section = []
        landuses_section.append('\n[LANDUSES]\n')
        landuses_section.append(';;Name           Sweeping_Interval Fraction_Available Last_Swept\n')
        landuses_section.append(';;-------------- ----------------- ------------------ -----------\n')
        for lu in used_landuses:
            landuses_section.append(f'{lu:<16} 7                 0.5                0\n')
        landuses_section.append('\n')

        # Insert LANDUSES section before SUBCATCHMENTS and modify SUBCATCHMENTS to include Landuse
        # First, find the end of the SUBCATCHMENTS section
        subcatchments_end_index = -1
        for i in range(subcatchments_index + 1, len(lines)):
            if lines[i].strip().startswith('[') and lines[i].strip() != '[SUBCATCHMENTS]':
                subcatchments_end_index = i
                break

        if subcatchments_end_index == -1:
            subcatchments_end_index = len(lines)

        # Modify SUBCATCHMENTS section to include Landuse column
        modified_subcatchments = []
        for i in range(subcatchments_index, subcatchments_end_index):
            line = lines[i]
            if line.strip().startswith(';;') or line.strip() == '[SUBCATCHMENTS]':
                # Keep header lines as is, but modify the column header line
                if 'Raingage' in line and 'Outlet' in line:
                    # This is the column header line, add Landuse column
                    modified_subcatchments.append(line.rstrip() + ' Landuse      \n')
                else:
                    modified_subcatchments.append(line)
            elif line.strip() and not line.strip().startswith(';;'):
                # This is a data line, add the landuse
                parts = line.strip().split()
                if len(parts) >= 8:  # Valid subcatchment line
                    subcatch_name = parts[0]
                    landuse = subcatchments.loc[subcatch_name, 'Landuse'] if subcatch_name in subcatchments.index else 'Residential'
                    modified_subcatchments.append(line.rstrip() + f'          {landuse}\n')
                else:
                    modified_subcatchments.append(line)
            else:
                modified_subcatchments.append(line)

        # Construct new_lines with LANDUSES section and modified SUBCATCHMENTS
        new_lines = (lines[:subcatchments_index] +
                    landuses_section +
                    modified_subcatchments +
                    lines[subcatchments_end_index:])

        # Step 2: POLLUTANTS section
        pollutant_sections = []
        pollutant_sections.append('\n\n[POLLUTANTS]\n')
        pollutant_sections.append(';;Name           Units  Crain Cgw   Kdecay SnowOnly CoPollutant CoFrac Cdwf Cinit\n')
        pollutant_sections.append(';;-------------- ------ ----- ---- ------ -------- ----------- ------ ---- -----\n')
        for name, units, crain, cgw, kdecay, snow_only, co_pollutant, co_frac, cdwf, cinit in zip(
            self.pollutants_data['Name'], self.pollutants_data['Units'], self.pollutants_data['Crain'],
            self.pollutants_data['Cgw'], self.pollutants_data['Kdecay'], self.pollutants_data['SnowOnly'],
            self.pollutants_data['CoPollutant'], self.pollutants_data['CoFrac'],
            self.pollutants_data['Cdwf'], self.pollutants_data['Cinit']
        ):
            co_pollutant_str = co_pollutant if co_pollutant else ''
            line = f'{name:<16} {units:<6} {crain:<5} {cgw:<4} {kdecay:<6} {snow_only:<8} {co_pollutant_str:<11} {co_frac:<6} {cdwf:<4} {cinit}\n'
            pollutant_sections.append(line)

        # Step 3: BUILDUP section
        pollutant_sections.append('\n\n[BUILDUP]\n')
        pollutant_sections.append(';;LandUse        Pollutant Function C1   C2  C3 PerArea\n')
        pollutant_sections.append(';;-------------- --------- -------- ---- --- -- -------\n')
        for lu in used_landuses:
            for pollutant in self.pollutants_data['Name']:
                pollutant_sections.append(f'{lu:<16} {pollutant:<9} EXP      100  0.5 0  AREA\n')

        # Step 4: WASHOFF section
        pollutant_sections.append('\n\n[WASHOFF]\n')
        pollutant_sections.append(';;LandUse        Pollutant Function C1  C2  C3 C4\n')
        pollutant_sections.append(';;-------------- --------- -------- --- --- -- --\n')
        for lu in used_landuses:
            for pollutant in self.pollutants_data['Name']:
                pollutant_sections.append(f'{lu:<16} {pollutant:<9} EXP      0.1 1.2 0  0\n')

        # Step 5: LOADINGS section
        pollutant_sections.append('\n\n[LOADINGS]\n')
        pollutant_sections.append(';;Subcatchment        LandUse           Percent\n')
        for subcatch, row in subcatchments.iterrows():
            landuse = row.get('Landuse', 'Residential') or 'Residential'
            pollutant_sections.append(f'{subcatch:<22} {landuse:<17} 100\n')

        # Write the modified file
        with open(output_file, 'w') as f:
            f.writelines(new_lines)
            f.writelines(pollutant_sections)


if __name__ == '__main__':
    unittest.main()
